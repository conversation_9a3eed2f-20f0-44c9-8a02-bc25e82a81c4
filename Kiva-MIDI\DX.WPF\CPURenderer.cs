using System;
using System.Windows;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using SharpDX.Direct3D11;
using SharpDX.DXGI;
using Device = SharpDX.Direct3D11.Device;

namespace Kiva_MIDI
{
    /// <summary>
    /// CPU-based renderer that bypasses DirectX for Wine compatibility
    /// </summary>
    public class CPURenderer : D3D
    {
        private WriteableBitmap bitmap;
        private int width, height;
        private byte[] pixelBuffer;
        private bool isDisposed = false;

        public CPURenderer()
        {
            System.Diagnostics.Debug.WriteLine("Using CPU renderer for Wine compatibility");
        }

        public override void Reset(int w, int h)
        {
            if (w < 1 || h < 1) return;

            width = w;
            height = h;
            
            // Create a WriteableBitmap for CPU rendering
            Application.Current.Dispatcher.Invoke(() =>
            {
                bitmap = new WriteableBitmap(width, height, 96, 96, PixelFormats.Bgra32, null);
            });
            
            // Create pixel buffer
            pixelBuffer = new byte[width * height * 4]; // BGRA format
            
            // Clear to black
            Array.Clear(pixelBuffer, 0, pixelBuffer.Length);
        }

        public override void BeginRender(DrawEventArgs args)
        {
            // Clear the buffer to background color (black for now)
            if (pixelBuffer != null)
            {
                Array.Clear(pixelBuffer, 0, pixelBuffer.Length);
            }
        }

        public override void EndRender(DrawEventArgs args)
        {
            if (bitmap != null && pixelBuffer != null)
            {
                Application.Current.Dispatcher.Invoke(() =>
                {
                    try
                    {
                        bitmap.Lock();
                        
                        // Copy pixel buffer to bitmap
                        var rect = new Int32Rect(0, 0, width, height);
                        bitmap.WritePixels(rect, pixelBuffer, width * 4, 0);
                        
                        bitmap.Unlock();
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"CPU renderer EndRender error: {ex.Message}");
                    }
                });
            }
        }

        public override void SetBackBuffer(DXImageSource dximage)
        {
            // For CPU rendering, we'll need to set the bitmap directly
            // This is a simplified implementation - in a full implementation,
            // you'd need to convert the WriteableBitmap to something the DXImageSource can use
            System.Diagnostics.Debug.WriteLine("CPU renderer SetBackBuffer called - simplified implementation");
        }

        public override WriteableBitmap ToImage()
        {
            return bitmap;
        }

        // Simulate basic drawing operations for CPU rendering
        public void DrawRectangle(int x, int y, int width, int height, byte r, byte g, byte b, byte a)
        {
            if (pixelBuffer == null) return;

            for (int py = y; py < y + height && py < this.height; py++)
            {
                for (int px = x; px < x + width && px < this.width; px++)
                {
                    if (px >= 0 && py >= 0)
                    {
                        int index = (py * this.width + px) * 4;
                        if (index + 3 < pixelBuffer.Length)
                        {
                            pixelBuffer[index] = b;     // Blue
                            pixelBuffer[index + 1] = g; // Green
                            pixelBuffer[index + 2] = r; // Red
                            pixelBuffer[index + 3] = a; // Alpha
                        }
                    }
                }
            }
        }

        public WriteableBitmap GetBitmap()
        {
            return bitmap;
        }

        protected override void Dispose(bool disposing)
        {
            if (!isDisposed)
            {
                if (disposing)
                {
                    bitmap = null;
                    pixelBuffer = null;
                }
                isDisposed = true;
            }
            base.Dispose(disposing);
        }

        public bool IsDisposed => isDisposed;

        // Properties expected by Scene class - return null for CPU rendering
        public Device Device => null;
        public Texture2D RenderTarget => null;
        public RenderTargetView RenderTargetView => null;
    }
}
