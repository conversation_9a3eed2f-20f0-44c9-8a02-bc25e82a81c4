using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.ComponentModel;

namespace Kiva_MIDI
{
    /// <summary>
    /// CPU-based image source that doesn't rely on DirectX sharing for Wine compatibility
    /// </summary>
    public class CPUImageSource : Image, INotifyPropertyChanged
    {
        private WriteableBitmap bitmap;
        private bool isDisposed = false;

        public CPUImageSource()
        {
            System.Diagnostics.Debug.WriteLine("Using CPU image source for Wine compatibility");
            this.Stretch = Stretch.Fill;
        }

        public void SetBitmap(WriteableBitmap newBitmap)
        {
            if (isDisposed) return;

            Application.Current.Dispatcher.Invoke(() =>
            {
                bitmap = newBitmap;
                this.Source = bitmap;
                OnPropertyChanged("Source");
            });
        }

        public void Invalidate()
        {
            if (isDisposed || bitmap == null) return;

            Application.Current.Dispatcher.Invoke(() =>
            {
                // Force a refresh of the image
                this.InvalidateVisual();
            });
        }

        public void Dispose()
        {
            if (isDisposed) return;

            Application.Current.Dispatcher.Invoke(() =>
            {
                this.Source = null;
                bitmap = null;
                isDisposed = true;
            });
        }

        public bool IsDisposed => isDisposed;

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
