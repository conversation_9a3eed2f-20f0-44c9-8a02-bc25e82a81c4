﻿<UserControl x:Class="Kiva_MIDI.AdvancedSettings"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:Kiva_MIDI"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition/>
            <ColumnDefinition/>
        </Grid.ColumnDefinitions>
        <StackPanel Margin="10">
            <DockPanel>
                <Label FontSize="14">Max Render Threads</Label>
                <local:NumberSelect x:Name="threadCount" Minimum="1" Value="1" Maximum="16" Margin="5,0" Width="70" HorizontalAlignment="Left" ValueChanged="threadCount_ValueChanged"/>
            </DockPanel>
            <DockPanel>
                <local:BetterCheckbox x:Name="forceSingleThread" Text="Force single thread" CheckToggled="forceSingleThread_CheckToggled"/>
                <local:InfoIcon Title="Single Threaded" Text="Forces the renderer to use only 1 thread. Note audio will still be rendered in different threads." HorizontalAlignment="Left" Margin="5,0,0,0"/>
            </DockPanel>
        </StackPanel>
        <StackPanel Margin="10" Grid.Column="1">
            <DockPanel HorizontalAlignment="Left">
                <Label FontSize="14">
                    Require Kiva Restart
                </Label>
                <local:InfoIcon Title="Require Kiva Restart" Text="Settings that require kiva to be restarted for them to take effect"/>
            </DockPanel>
            <DockPanel HorizontalAlignment="Left">
                <local:BetterCheckbox x:Name="disableTransparency" CheckToggled="disableTransparency_CheckToggled" Text="Disable window transparency"/>
                <local:InfoIcon Title="Disable transparency (compatibility)" Text="Sometimes programs like OBS have trouble recording the Kiva window. This setting can help fix that." Margin="5,1,0,0"/>
            </DockPanel>
            <DockPanel HorizontalAlignment="Left">
                <local:BetterCheckbox x:Name="forceCPURendering" CheckToggled="forceCPURendering_CheckToggled" Text="Force CPU rendering (Wine compatibility)"/>
                <local:InfoIcon Title="CPU Rendering" Text="Forces CPU-based rendering instead of DirectX. Useful for Wine compatibility on Linux." Margin="5,1,0,0"/>
            </DockPanel>
        </StackPanel>
    </Grid>
</UserControl>
