﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using SharpDX.DXGI;
using Direct3D11 = SharpDX.Direct3D11;
using Direct3D = SharpDX.Direct3D;

namespace Kiva_MIDI
{
	public static class DeviceUtil
	{
		public static SharpDX.Direct3D11.Device Create11(
			Direct3D11.DeviceCreationFlags cFlags = Direct3D11.DeviceCreationFlags.None,
			Direct3D.FeatureLevel minLevel = Direct3D.FeatureLevel.Level_9_1
		)
		{
			using (var dg = new DisposeGroup())
			{
				var level = Direct3D11.Device.GetSupportedFeatureLevel();
				if (level < minLevel)
					return null;

				// Try hardware first
				try
				{
					return new Direct3D11.Device(Direct3D.DriverType.Hardware, cFlags, level);
				}
				catch (Exception ex)
				{
					System.Diagnostics.Debug.WriteLine($"Hardware DirectX11 device creation failed: {ex.Message}");
				}

				// Fallback to WARP (software) renderer for Wine compatibility
				try
				{
					System.Diagnostics.Debug.WriteLine("Falling back to WARP (software) renderer for Wine compatibility");
					return new Direct3D11.Device(Direct3D.DriverType.Warp, cFlags, level);
				}
				catch (Exception ex)
				{
					System.Diagnostics.Debug.WriteLine($"WARP DirectX11 device creation failed: {ex.Message}");
				}

				// Final fallback to reference renderer
				try
				{
					System.Diagnostics.Debug.WriteLine("Falling back to reference renderer");
					return new Direct3D11.Device(Direct3D.DriverType.Reference, cFlags, level);
				}
				catch (Exception ex)
				{
					System.Diagnostics.Debug.WriteLine($"Reference DirectX11 device creation failed: {ex.Message}");
				}

				return null;
			}
		}
	}
}
